# Epic: Multi-Agent Context Builder Architecture

**Epic ID**: TBD
**Status**: Draft
**Priority**: High
**Assignee**: TBD
**Labels**: `AI-powered`, `duo-agent-platform`, `architecture`, `context-builder`, `multi-agent`
**Milestone**: TBD

---

## Executive Summary

The GitLab Duo Agent Platform's Context Builder agent represents a critical quality bottleneck that fundamentally limits the platform's ability to scale to world-class AI capabilities. This epic proposes a transformative multi-agent architecture that replaces the current single-agent approach with a sophisticated hierarchical system of specialized agents orchestrated by an intelligent coordinator.

**Business Impact**: The Context Builder is the foundation of all Duo workflows. Poor context quality cascades through planning and execution phases, directly impacting user success rates and satisfaction. This architecture enables scaling to 100+ tools while delivering 40-70% improvements in context quality and investigation efficiency.

**Technical Innovation**: This solution represents a significant advancement in GitLab's AI capabilities, demonstrating sophisticated multi-agent coordination with LLM-powered strategic intelligence—positioning GitLab as a leader in enterprise AI systems.

---

## Problem Statement

### Current Architecture Limitations

The existing Context Builder agent suffers from fundamental architectural constraints that prevent scaling and quality improvements:

#### 1. Tool Overwhelm Crisis
- **Current State**: Single agent binds 34+ static tools + dynamic MCP tools in flat schema
- **Selection Quality**: LLM struggles with combinatorial tool selection from large flat list
- **Scalability Blocker**: Scaling to more tools will decrease the quality fruther.

#### 2. Strategic Intelligence Gap
- **Generic Approach**: Same "gather broad context" strategy regardless of goal type
- **No Goal Classification**: CI/CD issues get same treatment as feature development
- **Missing Investigation Patterns**: No structured progression from overview to deep dive
- **Poor Tool Sequencing**: Random tool selection rather than purposeful investigation

#### 3. Investigation Quality Issues
- **Incomplete Coverage**: No systematic approach to ensure high coverage context gathering
- **Shallow Analysis**: Single agent cannot develop deep expertise across all domains
- **Weak Stopping Criteria**: Vague "sufficient information" assessment

### Evidence of Impact

**From Codebase Analysis**:

---

## Solution Architecture

### Multi-Agent Hierarchical Design

The proposed architecture transforms context building from a single overwhelmed agent into a coordinated team of domain specialists orchestrated by an intelligent coordinator.

#### Core Architectural Principles

1. **Hierarchical Specialization**: Domain experts with focused tool sets and deep knowledge
2. **Iterative Investigation**: Maintains successful iterative patterns while adding structure
3. **Integration**: Works within existing GitLab workflow infrastructure by continue using LangGraph
4. **Scaling**: Linear scaling to 100+ tools through specialist distribution

#### System Components

**Context Orchestrator Agent** (Meta-Agent)
- **Role**: Strategic coordinator with goal analysis and specialist management capabilities
- **Responsibilities**:
  - Goal classification and investigation strategy selection
  - Dynamic specialist routing based on problem type and context
  - Context synthesis from specialist findings
  - Iterative investigation management with gap assessment
  - Completion evaluation using sophisticated success criteria

**Repository Explorer Agent** (Codebase Specialist)
- **Domain**: Project structure, architecture patterns, configuration analysis
- **Tool Focus**: `list_dir`, `find_files`, `read_file`, `get_repository_file`, `grep`
- **Intelligence**: Recognizes monorepo vs microservices, identifies key files, understands project patterns

**Issue/MR Analyzer Agent** (Project Context Specialist)
- **Domain**: Development workflows, project history, collaboration patterns
- **Tool Focus**: `list_issues`, `get_issue`, `get_merge_request`, `gitlab_issue_search`, `list_merge_request_diffs`
- **Intelligence**: Analyzes development patterns, identifies related work, understands project dynamics

**CI/CD Infrastructure Agent** (Operations Specialist)
- **Domain**: Build systems, deployment pipelines, operational context
- **Tool Focus**: `get_pipeline_errors`, `get_job_logs`, `get_commit`, `run_git_command`
- **Intelligence**: Pipeline failure analysis, deployment pattern recognition, operational troubleshooting

**Code Navigator Agent** (Implementation Specialist)
- **Domain**: Code analysis, dependencies, implementation patterns
- **Tool Focus**: `gitlab_blob_search`, `grep`, `read_file`, `find_files`
- **Intelligence**: Dependency mapping, code pattern analysis, implementation strategy assessment

**Session Context Agent** (Continuity Specialist)
- **Domain**: Historical context, session continuity, work item management
- **Tool Focus**: `get_previous_session_context`, `create_work_item`, `get_work_item`
- **Intelligence**: Session state management, context continuity, work tracking

### Orchestration Intelligence

#### Goal-Aware Strategy Selection

The orchestrator employs sophisticated LLM reasoning to classify goals and select appropriate investigation strategies:

**Strategy Examples**:
```
CI/CD Failure Investigation:
├── Primary Specialists: [CI/CD Infrastructure, Repository Explorer]
├── Investigation Sequence: Pipeline Analysis → Config Review → Recent Changes
├── Success Criteria: Root cause identification + fix recommendations
└── Iteration Pattern: Failure-focused with escalation to code analysis if needed

Feature Development Planning:
├── Primary Specialists: [Repository Explorer, Code Navigator, Issue/MR Analyzer]
├── Investigation Sequence: Architecture Understanding → Pattern Analysis → Requirements Review
├── Success Criteria: Implementation approach + integration points + testing strategy
└── Iteration Pattern: Breadth-first with depth in relevant areas

Bug Investigation:
├── Primary Specialists: [Code Navigator, CI/CD Infrastructure, Issue/MR Analyzer]
├── Investigation Sequence: Error Analysis → Recent Changes → Related Issues
├── Success Criteria: Bug reproduction steps + root cause + impact assessment
└── Iteration Pattern: Hypothesis-driven with targeted deep dives
```

#### Iterative Investigation Management

The orchestrator maintains iterative capability while adding strategic structure:

**Investigation Flow**:
1. **Strategic Analysis**: Goal classification and strategy selection
2. **Initial Deployment**: Specialist task creation with clear objectives and context
3. **Iterative Refinement**: Gap assessment and targeted follow-up investigations
4. **Context Synthesis**: Intelligent aggregation of specialist findings
5. **Quality Assessment**: Completeness evaluation against goal-specific criteria

**Key Innovation**: Specialists receive not just the goal, but rich context from the orchestrator and other specialists, enabling informed tool selection and investigation focus.

#### Dynamic Tool Selection by Specialists

**Critical Design Decision**: Specialists use LLM reasoning for tool selection rather than deterministic sequences:

```
Specialist Investigation Pattern:
├── Receive: Goal + Context + Investigation Focus + Success Criteria
├── Analyze: Current understanding gaps and investigation priorities
├── Select Tools: Choose 0-N tools based on reasoning (not predetermined sequence)
├── Execute: Perform investigation with chosen tools
├── Synthesize: Create domain-specific report with findings and recommendations
└── Return: Structured findings to orchestrator for integration
```

This approach ensures specialists can:
- Skip unnecessary tools when context is already sufficient
- Adapt tool selection based on intermediate findings
- Focus investigation based on goal-specific requirements
- Provide intelligent analysis rather than raw tool outputs

---

## Technical Implementation

### Integration with Existing Infrastructure

The multi-agent system integrates seamlessly with GitLab's existing workflow infrastructure:

**LangGraph Integration**:
- Maintains existing StateGraph orchestration patterns
- Preserves all approval gates and human-in-the-loop mechanisms
- Integrates with current ToolsRegistry and ToolsExecutor infrastructure
- Supports existing MCP tool integration with intelligent distribution

**Workflow Compatibility**:
- Replaces single `build_context` node with multi-agent subgraph
- Maintains identical input/output interfaces for downstream phases
- Preserves conversation history and state management patterns
- Supports existing monitoring and metrics infrastructure

### Tool Distribution Strategy

**Intelligent Tool Allocation**:
```
Tool Distribution Matrix:
├── Repository Explorer: [list_dir, find_files, read_file, read_files, get_repository_file, grep]
├── Issue/MR Analyzer: [list_issues, get_issue, get_merge_request, gitlab_issue_search, list_merge_request_diffs]
├── CI/CD Infrastructure: [get_pipeline_errors, get_job_logs, get_commit, list_commits, run_git_command]
├── Code Navigator: [gitlab_blob_search, grep, read_file, find_files]
├── Session Context: [get_previous_session_context, create_work_item, get_work_item]
└── MCP Tools: Dynamically allocated based on tool capabilities and specialist domains
```

**Benefits**:
- 75% reduction in tool schema tokens per agent (from ~4,735 to ~1,200)
- Specialists become experts in their tool domains
- Linear scaling to 100+ tools through domain distribution
- Maintains all existing tool capabilities and approval mechanisms

### State Management and Context Passing

**Enhanced Context Flow**:
```
Context Propagation Pattern:
├── User Goal → Orchestrator (strategic analysis)
├── Orchestrator → Specialists (goal + context + focus + criteria)
├── Specialists → Tools (focused investigation)
├── Tools → Specialists (raw findings)
├── Specialists → Orchestrator (synthesized domain reports)
├── Orchestrator → Quality Assessment (completeness evaluation)
└── Orchestrator → Handover (final context synthesis)
```

**State Enrichment**: Each specialist receives comprehensive context including:
- Original user goal and project information
- Current investigation strategy and focus areas
- Findings from other specialists (when relevant)
- Success criteria and quality expectations
- Iteration history and previous investigation results

---

## Implementation Plan

### Phase 1: Foundation Architecture (3-4 weeks)

**Objectives**: Establish basic multi-agent structure with existing tool integration

**Deliverables**:
- Create specialist agent classes with domain-specific prompts
- Implement basic orchestrator with hardcoded routing logic
- Integrate with existing ToolsRegistry and ToolsExecutor
- Maintain all approval and handover mechanisms
- Add comprehensive logging and metrics for multi-agent interactions

**Success Criteria**:
- Multi-agent system produces equivalent context quality to current implementation
- No regression in latency or user experience
- All existing approval flows and integrations work correctly

### Phase 2: Strategic Intelligence (4-5 weeks)

**Objectives**: Add LLM-powered orchestration and goal-aware strategies

**Deliverables**:
- Implement goal classification and strategy selection logic
- Add dynamic specialist routing based on problem type
- Create context synthesis and quality assessment capabilities
- Enhance iterative investigation with gap analysis
- Develop specialist domain intelligence and reasoning

**Success Criteria**:
- Demonstrable improvement in context relevance for different goal types
- Successful specialist coordination with minimal redundant tool calls
- Quality assessment accurately identifies investigation gaps

### Phase 3: Optimization and Enhancement (3-4 weeks)

**Objectives**: Optimize performance and add advanced capabilities

**Deliverables**:
- Performance optimization for parallel specialist execution
- Advanced MCP tool distribution logic
- Comprehensive metrics and monitoring dashboard
- Fine-tuned specialist prompts and intelligence
- Documentation and operational runbooks

**Success Criteria**:
- 40-70% improvement in context quality metrics
- No latency regression compared to current implementation
- Successful handling of 50+ tools with room for expansion

**Total Implementation Timeline**: 10-13 weeks

### Risk Mitigation

**Technical Risks**:
- **Latency Concerns**: Mitigate through parallel specialist execution and intelligent caching
- **State Management Complexity**: Leverage existing LangGraph patterns and comprehensive testing
- **Integration Breakage**: Maintain strict backward compatibility and gradual rollout

**Quality Risks**:
- **Coordination Failures**: Implement robust error handling and fallback to single-agent mode
- **Context Loss**: Ensure comprehensive state passing and validation
- **Investigation Gaps**: Add multiple quality gates and completeness verification

**Operational Risks**:
- **Deployment Complexity**: Use feature flags for gradual rollout and A/B testing
- **Monitoring Blind Spots**: Implement detailed multi-agent performance metrics
- **Debugging Challenges**: Add comprehensive logging and agent interaction tracing

---


### Validation Methodology

**Technical Validation**:
- A/B testing against current implementation with identical goals
- Tool selection relevance scoring by domain experts
- Context completeness assessment using structured rubrics
- Performance benchmarking across different goal types

**Quality Validation**:
- Human evaluation of context quality by GitLab engineers
- Downstream planning phase success rate analysis
- User satisfaction surveys and feedback collection
- Long-term workflow success rate tracking

**Business Validation**:
- Development velocity measurement from context to execution
- User adoption and engagement pattern analysis
- Support ticket reduction related to context quality issues
- Customer satisfaction impact assessment

---

## Long-term Extensibility

### Future Specialist Agents

The architecture enables seamless addition of new specialists:

**Security Analysis Agent**: Vulnerability assessment, compliance checking, security pattern analysis
**Performance Optimization Agent**: Performance bottleneck identification, optimization recommendations
**Documentation Agent**: Documentation quality assessment, knowledge gap identification
**Testing Strategy Agent**: Test coverage analysis, testing approach recommendations

### Cross-Workflow Intelligence

Specialists can be shared across different workflow types:
- Repository Explorer works across software development, CI/CD conversion, and issue-to-MR flows
- Code Navigator provides value in code review, security analysis, and refactoring workflows
- CI/CD Infrastructure Agent supports deployment, monitoring, and troubleshooting scenarios


---

## Current Implementation Deep Dive

### Existing Context Builder Analysis

**Current Prompt Strategy**:
```jinja
You are an experienced GitLab user.
Given a goal set by Human and a set of tools available to you:
  1. Check what information is available in the current working directory with the `list_dir` tool.
  2. Prepare all available tool calls to gather broad context information.
  3. Avoid making any recommendations on how to achieve the goal.
  4. Avoid making any changes to the current working directory; implementation is going to be done by the Human.
  5. Once you have gathered all necessary information, you must call the tool `{{handover_tool_name}}` to complete your goal.
```

**Critical Issues with Current Approach**:
- Generic "gather broad context" instruction lacks strategic intelligence
- No goal-specific investigation patterns or prioritization
- Vague stopping criteria ("all necessary information")
- Single-pass approach without structured exploration phases

**Current Tool Execution Flow**:
```
Current Execution Pattern:
├── Agent Decision: LLM selects tools from 34+ tool flat schema
├── Router Logic: Determines if tools need approval, should execute, or handover
├── Tools Executor: Validates and executes tool calls sequentially
├── Approval Gates: Human approval required for non-preapproved tools
├── Supervisor Nudging: PlanSupervisorAgent sends generic nudging messages
└── Iteration: Process repeats until handover tool called
```

**Identified Bottlenecks**:
- Tool overwhelm: 34+ tools + MCP tools create cognitive overload
- No strategic tool sequencing or investigation phases
- Generic supervisor nudging without domain-specific guidance
- Flat tool schema prevents hierarchical reasoning about tool relationships

### MCP Tool Integration Analysis

**Current MCP Integration**:
```python
def toolset(self, tool_names: list[str]) -> Toolset:
    # MCP tools if there are any are added to toolset
    tool_names += self._mcp_tool_names

    all_tools = {
        tool_name: self._enabled_tools[tool_name]
        for tool_name in tool_names
        if tool_name in self._enabled_tools
    }
```

**MCP Integration Challenges**:
- Dynamic tools added to already overwhelming tool list
- No intelligent distribution of MCP tools based on capabilities
- Flat addition to existing tool schema without categorization
- No domain-specific allocation of external tools

**Proposed MCP Enhancement**:
- Analyze MCP tool capabilities and automatically assign to appropriate specialists
- Dynamic tool allocation based on investigation strategy and specialist needs
- Intelligent caching and reuse of MCP tool results across specialists
- Domain-specific MCP tool filtering and prioritization

### Metrics and Monitoring Gaps

**Current Metrics Available**:
```python
# From duo_workflow_metrics.py
self.tool_call_duration = Histogram(
    "duo_workflow_tool_call_seconds",
    "Duration of tool calls in Duo Workflow",
    ["tool_name", "flow_type"],
)

self.agent_platform_tool_failure_counter = Counter(
    "duo_workflow_agent_platform_tool_failure_total",
    "Number of tool failures in Agent Platform",
    ["flow_type", "tool_name", "failure_reason"],
)
```

**Missing Critical Metrics**:
- Context quality and completeness assessment
- Investigation strategy effectiveness
- Tool selection relevance and accuracy
- Specialist coordination efficiency
- Context synthesis quality

**Proposed Enhanced Metrics**:
```python
# New metrics for multi-agent system
context_quality_score = Histogram(
    "duo_workflow_context_quality_score",
    "Quality assessment of gathered context",
    ["goal_type", "specialist_combination"],
)

specialist_coordination_efficiency = Histogram(
    "duo_workflow_specialist_coordination_seconds",
    "Time spent on specialist coordination",
    ["orchestrator_strategy", "specialist_count"],
)

investigation_completeness = Gauge(
    "duo_workflow_investigation_completeness_ratio",
    "Ratio of investigation goals achieved",
    ["goal_type", "iteration_count"],
)
```

---

## Architecture Diagrams

### Current Single-Agent Architecture

```
Current Context Builder Flow:
┌─────────────────────────────────────────────────────────────┐
│                    Context Builder Agent                    │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              34+ Tools + MCP Tools                      ││
│  │  [list_dir, get_issue, get_merge_request, ...]         ││
│  │  + Dynamic MCP Tools                                    ││
│  │  = ~4,735 tokens in tool schemas                       ││
│  └─────────────────────────────────────────────────────────┘│
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │           Generic "Gather Broad Context"                ││
│  │              No Goal Classification                     ││
│  │              No Strategic Intelligence                  ││
│  └─────────────────────────────────────────────────────────┘│
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Random Tool Selection                      ││
│  │           Poor Tool Sequencing                          ││
│  │           Cognitive Overload                            ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │  Planning Phase │ ← Poor Context Quality
                  │   (Impacted)    │
                  └─────────────────┘
```

### Proposed Multi-Agent Architecture

```
Multi-Agent Context Builder System:
┌─────────────────────────────────────────────────────────────────────────────┐
│                        Context Orchestrator Agent                          │
│  ┌─────────────────────────────────────────────────────────────────────────┐│
│  │  🧠 LLM-Powered Strategic Intelligence                                 ││
│  │  • Goal Analysis & Classification                                      ││
│  │  • Investigation Strategy Selection                                    ││
│  │  • Dynamic Specialist Routing                                         ││
│  │  • Context Synthesis & Quality Assessment                             ││
│  │  • Iterative Investigation Management                                 ││
│  └─────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼
┌─────────────────────────┐ ┌─────────────────────────┐ ┌─────────────────────────┐
│  Repository Explorer    │ │   Issue/MR Analyzer     │ │ CI/CD Infrastructure    │
│       Agent             │ │        Agent            │ │        Agent            │
│  ┌─────────────────────┐│ │  ┌─────────────────────┐│ │  ┌─────────────────────┐│
│  │  8-10 Focused Tools│ │ │  │  8-10 Focused Tools│ │ │  │  8-10 Focused Tools││
│  │ list_dir, find_files││ │  │ list_issues, get_mr ││ │  │ get_pipeline_errors ││
│  │ read_file, grep     ││ │  │ gitlab_issue_search ││ │  │ get_job_logs        ││
│  │ ~1,200 tokens       ││ │  │ ~1,200 tokens       ││ │  │ ~1,200 tokens       ││
│  └─────────────────────┘│ │  └─────────────────────┘│ │  └─────────────────────┘│
│  ┌─────────────────────┐│ │  ┌─────────────────────┐│ │  ┌─────────────────────┐│
│  │Domain Intelligence  ││ │  │Domain Intelligence  ││ │  │Domain Intelligence  ││
│  │• Project patterns   ││ │  │• Dev workflows      ││ │  │• Pipeline analysis  ││
│  │• Architecture types ││ │  │• Collaboration      ││ │  │• Failure patterns   ││
│  │• Key file detection ││ │  │• Issue relationships││ │  │• Deployment context ││
│  └─────────────────────┘│ │  └─────────────────────┘│ │  └─────────────────────┘│
└─────────────────────────┘ └─────────────────────────┘ └─────────────────────────┘
                    │               │               │
                    └───────────────┼───────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼
┌─────────────────────────┐ ┌─────────────────────────┐ ┌─────────────────────────┐
│   Code Navigator        │ │  Session Context        │ │    Integration Layer    │
│       Agent             │ │       Agent             │ │                         │
│  ┌─────────────────────┐│ │  ┌─────────────────────┐│ │  ┌─────────────────────┐│
│  │   8-10 Focused Tools││ │  │   6-8 Focused Tools ││ │  │  Tool Manager       ││
│  │ gitlab_blob_search  ││ │  │ get_previous_session││ │  │  State Manager      ││
│  │ grep, read_file     ││ │  │ create_work_item    ││ │  │  Quality Controller ││
│  │ ~1,200 tokens       ││ │  │ ~1,000 tokens       ││ │  │                     ││
│  └─────────────────────┘│ │  └─────────────────────┘│ │  └─────────────────────┘│
│  ┌─────────────────────┐│ │  ┌─────────────────────┐│ │                         │
│  │Domain Intelligence  ││ │  │Domain Intelligence  ││ │  Integrates with:       │
│  │• Code dependencies  ││ │  │• Session continuity ││ │  • ToolsRegistry        │
│  │• Implementation     ││ │  │• Context tracking   ││ │  • ToolsExecutor        │
│  │• Pattern analysis   ││ │  │• Work item mgmt     ││ │  • ToolsApproval        │
│  └─────────────────────┘│ │  └─────────────────────┘│ │  • HandoverAgent        │
└─────────────────────────┘ └─────────────────────────┘ └─────────────────────────┘
                                    │
                                    ▼
                          ┌─────────────────┐
                          │  Planning Phase │ ← High-Quality Context
                          │   (Enhanced)    │
                          └─────────────────┘
```

---
