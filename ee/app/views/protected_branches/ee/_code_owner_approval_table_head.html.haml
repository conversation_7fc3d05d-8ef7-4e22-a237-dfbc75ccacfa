- if protected_branch_entity.feature_available?(:code_owner_approval_required)
  %th
    = s_("ProtectedBranch|Code owner approval")
    %span.gl-sr-only
      = "(" << s_("ProtectedBranch|Does not apply to users **Allowed to push** when pushing directly to the branch. Optional sections are not enforced.") << ")"
    %span.has-tooltip{ data: { container: 'body' }, title: s_('ProtectedBranch|Does not apply to users **Allowed to push** when pushing directly to the branch. Optional sections are not enforced.'), 'aria-hidden': 'true' }
      = sprite_icon('question-o', size: 16, css_class: 'gl-fill-icon-info')
